import { supabaseWithRefresh } from './supabaseWithRefresh';

export interface PostDetailData {
  id: string;
  userInitials: string;
  userName: string;
  timeAgo: string;
  soberDays: number;
  content: string;
  likes: number;
  supports: number;
  comments: number;
  userColor: string;
  mood?: string;
  image?: string;
  user_id: string;
  created_at: string;
  isLiked?: boolean;
  isSupported?: boolean;
}

export interface CommentData {
  id: string;
  userInitials: string;
  userName: string;
  timeAgo: string;
  content: string;
  likes: number;
  replies: number;
  userColor: string;
  user_id: string;
  created_at: string;
  isLiked?: boolean;
  replies_list?: CommentData[];
}

export interface PostDetailResponse {
  post: PostDetailData | null;
  comments: CommentData[];
  error?: string;
}

/**
 * Helper function to get user initials from name
 */
const getUserInitials = (name: string): string => {
  if (!name) return '👤';
  const names = name.split(' ');
  if (names.length >= 2) {
    return `${names[0][0]}${names[1][0]}`.toUpperCase();
  }
  return names[0][0].toUpperCase();
};

/**
 * Helper function to get user color based on user ID
 */
const getUserColor = (userId: string): string => {
  const colors = [
    'bg-blue-900/30',
    'bg-purple-900/30',
    'bg-amber-900/30',
    'bg-green-900/30',
    'bg-red-900/30',
    'bg-indigo-900/30',
    'bg-pink-900/30',
    'bg-teal-900/30'
  ];
  const index = userId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length;
  return colors[index];
};

/**
 * Helper function to format time ago
 */
const getTimeAgo = (createdAt: string): string => {
  const now = new Date();
  const created = new Date(createdAt);
  const diffInMinutes = Math.floor((now.getTime() - created.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays}d ago`;
  
  return created.toLocaleDateString();
};

/**
 * Generate mock user data based on user ID
 */
const getMockUserData = (userId: string) => {
  const mockNames = ['Alex Johnson', 'Sarah Miller', 'Mike Kennedy', 'Emma Davis', 'John Smith', 'Lisa Brown'];
  const nameIndex = userId.split('').reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0) % mockNames.length;
  return {
    name: mockNames[nameIndex],
    email: `user${userId.slice(-4)}@example.com`
  };
};

/**
 * Fetch complete post details with comments and interactions
 */
export const fetchPostDetail = async (postId: string, currentUserId?: string): Promise<PostDetailResponse> => {
  try {
    console.log('🔄 Fetching post detail for:', postId);

    // Fetch post data with interaction counts
    const { data: postData, error: postError } = await supabaseWithRefresh
      .from('posts')
      .select(`
        id,
        content,
        mood,
        image_url,
        created_at,
        user_id,
        post_likes(count),
        post_supports(count),
        post_comments(count)
      `)
      .eq('id', postId)
      .single();

    if (postError) {
      console.error('❌ Error fetching post:', postError);
      return { post: null, comments: [], error: 'Post not found' };
    }

    if (!postData) {
      return { post: null, comments: [], error: 'Post not found' };
    }

    // Fetch user's sober days from current streak
    const { data: streakData } = await supabaseWithRefresh
      .from('pledge_current_streaks')
      .select('current_streak_started_on')
      .eq('user_id', postData.user_id)
      .single();

    // Calculate sober days
    let soberDays = 0;
    if (streakData?.current_streak_started_on) {
      const startDate = new Date(streakData.current_streak_started_on);
      const today = new Date();
      const diffTime = today.getTime() - startDate.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      soberDays = Math.max(0, diffDays);
    }

    // Fetch current user's interactions if logged in
    let isLiked = false;
    let isSupported = false;

    if (currentUserId) {
      // Check if user liked the post
      const { data: likeData } = await supabaseWithRefresh
        .from('post_likes')
        .select('id')
        .eq('post_id', postId)
        .eq('user_id', currentUserId)
        .single();

      isLiked = !!likeData;

      // Check if user supported the post
      const { data: supportData } = await supabaseWithRefresh
        .from('post_supports')
        .select('id')
        .eq('post_id', postId)
        .eq('user_id', currentUserId)
        .single();

      isSupported = !!supportData;
    }

    // Generate mock user data for post author
    const postUser = getMockUserData(postData.user_id);

    // Transform post data
    const post: PostDetailData = {
      id: postData.id,
      user_id: postData.user_id,
      userInitials: getUserInitials(postUser.name),
      userName: postUser.name,
      timeAgo: getTimeAgo(postData.created_at),
      soberDays,
      content: postData.content,
      likes: postData.post_likes?.[0]?.count || 0,
      supports: postData.post_supports?.[0]?.count || 0,
      comments: postData.post_comments?.[0]?.count || 0,
      userColor: getUserColor(postData.user_id),
      mood: postData.mood,
      image: postData.image_url,
      created_at: postData.created_at,
      isLiked,
      isSupported
    };

    // Fetch comments with replies
    const { data: commentsData, error: commentsError } = await supabaseWithRefresh
      .from('post_comments')
      .select(`
        id,
        content,
        created_at,
        user_id,
        parent_comment_id
      `)
      .eq('post_id', postId)
      .order('created_at', { ascending: true });

    if (commentsError) {
      console.error('❌ Error fetching comments:', commentsError);
      // Don't return an error for comments - just log it and continue with empty comments
      // This prevents showing error modals when there are simply no comments
      console.log('📝 Continuing without comments due to error');
    }

    // Process comments and build threaded structure
    const comments: CommentData[] = [];
    const commentMap = new Map<string, CommentData>();

    if (commentsData && !commentsError) {
      // First pass: create all comments
      for (const comment of commentsData) {
        const commentUser = getMockUserData(comment.user_id);
        
        const commentData: CommentData = {
          id: comment.id,
          user_id: comment.user_id,
          userInitials: getUserInitials(commentUser.name),
          userName: commentUser.name,
          timeAgo: getTimeAgo(comment.created_at),
          content: comment.content,
          likes: 0, // Comment likes not implemented yet
          replies: 0, // Will be calculated
          userColor: getUserColor(comment.user_id),
          created_at: comment.created_at,
          isLiked: false, // Comment likes not implemented yet
          replies_list: []
        };

        commentMap.set(comment.id, commentData);
      }

      // Second pass: build threaded structure
      for (const comment of commentsData) {
        const commentData = commentMap.get(comment.id);
        if (!commentData) continue;

        if (comment.parent_comment_id) {
          // This is a reply
          const parentComment = commentMap.get(comment.parent_comment_id);
          if (parentComment) {
            parentComment.replies_list = parentComment.replies_list || [];
            parentComment.replies_list.push(commentData);
            parentComment.replies = parentComment.replies_list.length;
          }
        } else {
          // This is a top-level comment
          comments.push(commentData);
        }
      }
    }

    console.log(`✅ Loaded post with ${comments.length} top-level comments`);
    return { post, comments };

  } catch (error) {
    console.error('❌ Unexpected error fetching post detail:', error);
    return { post: null, comments: [], error: 'An unexpected error occurred' };
  }
};

/**
 * Submit a new comment or reply to a post
 */
export const submitComment = async (
  postId: string, 
  userId: string, 
  content: string, 
  parentCommentId?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    console.log('🔄 Submitting comment to post:', postId);

    const { error } = await supabaseWithRefresh
      .from('post_comments')
      .insert({
        post_id: postId,
        user_id: userId,
        content: content.trim(),
        parent_comment_id: parentCommentId || null
      });

    if (error) {
      console.error('❌ Error submitting comment:', error);
      return { success: false, error: 'Failed to submit comment' };
    }

    console.log('✅ Comment submitted successfully');
    return { success: true };

  } catch (error) {
    console.error('❌ Unexpected error submitting comment:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
};

/**
 * Delete a post and all its related data
 */
export const deletePost = async (
  postId: string,
  userId: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    console.log('🔄 Deleting post:', postId);

    // First verify the user owns this post
    const { data: postData, error: fetchError } = await supabaseWithRefresh
      .from('posts')
      .select('user_id')
      .eq('id', postId)
      .single();

    if (fetchError) {
      console.error('❌ Error fetching post for deletion:', fetchError);
      return { success: false, error: 'Post not found' };
    }

    if (!postData || postData.user_id !== userId) {
      console.error('❌ User does not own this post');
      return { success: false, error: 'You can only delete your own posts' };
    }

    // Delete the post (related data will be deleted via CASCADE if configured)
    const { error: deleteError } = await supabaseWithRefresh
      .from('posts')
      .delete()
      .eq('id', postId)
      .eq('user_id', userId); // Extra safety check

    if (deleteError) {
      console.error('❌ Error deleting post:', deleteError);
      return { success: false, error: 'Failed to delete post' };
    }

    console.log('✅ Post deleted successfully');
    return { success: true };

  } catch (error) {
    console.error('❌ Unexpected error deleting post:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
};
