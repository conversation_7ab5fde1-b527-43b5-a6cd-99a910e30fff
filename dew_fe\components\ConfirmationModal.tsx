import React from 'react';
import {
  Modal,
  View,
  Text,
  Pressable,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';

interface ConfirmationModalProps {
  visible: boolean;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  loading?: boolean;
  emoji?: string;
  destructive?: boolean;
}

const ConfirmationModal = ({
  visible,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  loading = false,
  emoji = '❓',
  destructive = false
}: ConfirmationModalProps) => {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View 
        className="flex-1 justify-center items-center"
        style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}
      >
        <View 
          className="mx-6 rounded-2xl p-6 shadow-2xl"
          style={{
            backgroundColor: Colors.background.elevated,
            borderWidth: 1,
            borderColor: 'rgba(255,255,255,0.1)',
            maxWidth: 340,
            width: '100%'
          }}
        >
          {/* Header */}
          <View className="items-center mb-4">
            <Text className="text-4xl mb-3">{emoji}</Text>
            <Text 
              style={{ color: Colors.text.primary }} 
              className="text-xl font-bold text-center"
            >
              {title}
            </Text>
          </View>

          {/* Message */}
          <Text 
            style={{ color: Colors.text.secondary }} 
            className="text-center leading-relaxed mb-6"
          >
            {message}
          </Text>

          {/* Buttons */}
          <View className="flex-row space-x-3">
            {/* Cancel Button */}
            <Pressable
              onPress={onCancel}
              disabled={loading}
              className="flex-1 py-3 px-4 rounded-xl border"
              style={{
                backgroundColor: Colors.background.secondary,
                borderColor: 'rgba(255,255,255,0.1)',
                opacity: loading ? 0.6 : 1
              }}
            >
              <Text 
                style={{ color: Colors.text.primary }} 
                className="text-center font-medium"
              >
                {cancelText}
              </Text>
            </Pressable>

            {/* Confirm Button */}
            <Pressable
              onPress={onConfirm}
              disabled={loading}
              className="flex-1 py-3 px-4 rounded-xl"
              style={{
                backgroundColor: destructive ? Colors.status.error : Colors.brand.primary,
                opacity: loading ? 0.6 : 1
              }}
            >
              {loading ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text className="text-white text-center font-medium">
                  {confirmText}
                </Text>
              )}
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default ConfirmationModal;
