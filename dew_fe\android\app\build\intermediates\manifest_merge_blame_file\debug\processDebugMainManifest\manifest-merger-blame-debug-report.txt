1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.javedakeeb.ai_therapist"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:4:3-75
11-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:2:3-64
12-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission
13-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:3:3-77
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:3:20-75
15        android:maxSdkVersion="32" />
15-->[BareExpo:expo.modules.image:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1bfcdadd5814b12738a6ba8ffa3b999\transformed\expo.modules.image-2.4.0\AndroidManifest.xml:17:9-35
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:5:3-63
16-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:5:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:6:3-78
17-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:6:20-76
18
19    <queries>
19-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:7:3-13:13
20        <intent>
20-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:8:5-12:14
21            <action android:name="android.intent.action.VIEW" />
21-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:9:7-58
21-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:9:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:10:7-67
23-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:10:17-65
24
25            <data android:scheme="https" />
25-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:11:7-37
25-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:11:13-35
26        </intent>
27
28        <package android:name="host.exp.exponent" />
28-->[:expo-dev-launcher] D:\side_project\dew_fe\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
28-->[:expo-dev-launcher] D:\side_project\dew_fe\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
29
30        <intent>
30-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:15:9-19:18
31
32            <!-- Required for picking images from the camera roll if targeting API 30 -->
33            <action android:name="android.media.action.IMAGE_CAPTURE" />
33-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:13-73
33-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:18:21-70
34        </intent>
35        <intent>
35-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:20:9-24:18
36
37            <!-- Required for picking images from the camera if targeting API 30 -->
38            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
38-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:13-80
38-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:23:21-77
39        </intent>
40        <intent>
40-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:8:9-14:18
41            <action android:name="android.intent.action.GET_CONTENT" />
41-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:13-72
41-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:9:21-69
42
43            <category android:name="android.intent.category.OPENABLE" />
43-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:13-73
43-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:11:23-70
44
45            <data android:mimeType="*/*" />
45-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:11:7-37
46        </intent> <!-- Query open documents -->
47        <intent>
47-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
48            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
48-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
48-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
49        </intent>
50        <intent>
50-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88692d87c8bd81b3c7040f478064fc4\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:8:9-12:18
51
52            <!-- Required for opening tabs if targeting API 30 -->
53            <action android:name="android.support.customtabs.action.CustomTabsService" />
53-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88692d87c8bd81b3c7040f478064fc4\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:13-90
53-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88692d87c8bd81b3c7040f478064fc4\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:21-87
54        </intent>
55    </queries>
56    <!--
57  Allows Glide to monitor connectivity status and restart failed requests if users go from a
58  a disconnected to a connected network state.
59    -->
60    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Required for picking images from camera directly -->
60-->[BareExpo:expo.modules.image:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1bfcdadd5814b12738a6ba8ffa3b999\transformed\expo.modules.image-2.4.0\AndroidManifest.xml:12:5-79
60-->[BareExpo:expo.modules.image:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1bfcdadd5814b12738a6ba8ffa3b999\transformed\expo.modules.image-2.4.0\AndroidManifest.xml:12:22-76
61    <uses-permission android:name="android.permission.CAMERA" />
61-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:5-65
61-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:8:22-62
62    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
62-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
62-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
63    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
63-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
63-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
64
65    <permission
65-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
66        android:name="com.javedakeeb.ai_therapist.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
66-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
67        android:protectionLevel="signature" />
67-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
68
69    <uses-permission android:name="com.javedakeeb.ai_therapist.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
69-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
69-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
70
71    <application
71-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:14:3-31:17
72        android:name="com.javedakeeb.ai_therapist.MainApplication"
72-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:14:16-47
73        android:allowBackup="true"
73-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:14:162-188
74        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
74-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
75        android:debuggable="true"
76        android:extractNativeLibs="false"
77        android:icon="@mipmap/ic_launcher"
77-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:14:81-115
78        android:label="@string/app_name"
78-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:14:48-80
79        android:roundIcon="@mipmap/ic_launcher_round"
79-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:14:116-161
80        android:supportsRtl="true"
80-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:14:221-247
81        android:theme="@style/AppTheme"
81-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:14:189-220
82        android:usesCleartextTraffic="true" >
82-->D:\side_project\dew_fe\android\app\src\debug\AndroidManifest.xml:6:18-53
83        <meta-data
83-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:15:5-83
84            android:name="expo.modules.updates.ENABLED"
84-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:15:16-59
85            android:value="false" />
85-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:15:60-81
86        <meta-data
86-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:16:5-105
87            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
87-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:16:16-80
88            android:value="ALWAYS" />
88-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:16:81-103
89        <meta-data
89-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:17:5-99
90            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
90-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:17:16-79
91            android:value="0" />
91-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:17:80-97
92
93        <activity
93-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:18:5-30:16
94            android:name="com.javedakeeb.ai_therapist.MainActivity"
94-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:18:15-43
95            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
95-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:18:44-134
96            android:exported="true"
96-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:18:256-279
97            android:launchMode="singleTask"
97-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:18:135-166
98            android:screenOrientation="portrait"
98-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:18:280-316
99            android:theme="@style/Theme.App.SplashScreen"
99-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:18:210-255
100            android:windowSoftInputMode="adjustResize" >
100-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:18:167-209
101            <intent-filter>
101-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:19:7-22:23
102                <action android:name="android.intent.action.MAIN" />
102-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:20:9-60
102-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:20:17-58
103
104                <category android:name="android.intent.category.LAUNCHER" />
104-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:21:9-68
104-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:21:19-66
105            </intent-filter>
106            <intent-filter>
106-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:23:7-29:23
107                <action android:name="android.intent.action.VIEW" />
107-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:9:7-58
107-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:9:15-56
108
109                <category android:name="android.intent.category.DEFAULT" />
109-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:25:9-67
109-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:25:19-65
110                <category android:name="android.intent.category.BROWSABLE" />
110-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:10:7-67
110-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:10:17-65
111
112                <data android:scheme="aitherapist" />
112-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:11:7-37
112-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:11:13-35
113                <data android:scheme="exp+aitherapist" />
113-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:11:7-37
113-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:11:13-35
114            </intent-filter>
115        </activity>
116
117        <provider
117-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
118            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
118-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
119            android:authorities="com.javedakeeb.ai_therapist.fileprovider"
119-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
120            android:exported="false"
120-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
121            android:grantUriPermissions="true" >
121-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
122            <meta-data
122-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
123                android:name="android.support.FILE_PROVIDER_PATHS"
123-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
124                android:resource="@xml/file_provider_paths" />
124-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
125        </provider>
126
127        <activity
127-->[:expo-dev-launcher] D:\side_project\dew_fe\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
128            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
128-->[:expo-dev-launcher] D:\side_project\dew_fe\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
129            android:exported="true"
129-->[:expo-dev-launcher] D:\side_project\dew_fe\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
130            android:launchMode="singleTask"
130-->[:expo-dev-launcher] D:\side_project\dew_fe\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
131            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
131-->[:expo-dev-launcher] D:\side_project\dew_fe\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
132            <intent-filter>
132-->[:expo-dev-launcher] D:\side_project\dew_fe\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
133                <action android:name="android.intent.action.VIEW" />
133-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:9:7-58
133-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:9:15-56
134
135                <category android:name="android.intent.category.DEFAULT" />
135-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:25:9-67
135-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:25:19-65
136                <category android:name="android.intent.category.BROWSABLE" />
136-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:10:7-67
136-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:10:17-65
137
138                <data android:scheme="expo-dev-launcher" />
138-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:11:7-37
138-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:11:13-35
139            </intent-filter>
140        </activity>
141        <activity
141-->[:expo-dev-launcher] D:\side_project\dew_fe\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
142            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
142-->[:expo-dev-launcher] D:\side_project\dew_fe\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
143            android:screenOrientation="portrait"
143-->[:expo-dev-launcher] D:\side_project\dew_fe\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
144            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
144-->[:expo-dev-launcher] D:\side_project\dew_fe\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
145        <activity
145-->[:expo-dev-menu] D:\side_project\dew_fe\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
146            android:name="expo.modules.devmenu.DevMenuActivity"
146-->[:expo-dev-menu] D:\side_project\dew_fe\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
147            android:exported="true"
147-->[:expo-dev-menu] D:\side_project\dew_fe\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
148            android:launchMode="singleTask"
148-->[:expo-dev-menu] D:\side_project\dew_fe\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
149            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
149-->[:expo-dev-menu] D:\side_project\dew_fe\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
150            <intent-filter>
150-->[:expo-dev-menu] D:\side_project\dew_fe\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
151                <action android:name="android.intent.action.VIEW" />
151-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:9:7-58
151-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:9:15-56
152
153                <category android:name="android.intent.category.DEFAULT" />
153-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:25:9-67
153-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:25:19-65
154                <category android:name="android.intent.category.BROWSABLE" />
154-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:10:7-67
154-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:10:17-65
155
156                <data android:scheme="expo-dev-menu" />
156-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:11:7-37
156-->D:\side_project\dew_fe\android\app\src\main\AndroidManifest.xml:11:13-35
157            </intent-filter>
158        </activity>
159
160        <meta-data
160-->[:expo-modules-core] D:\side_project\dew_fe\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
161            android:name="org.unimodules.core.AppLoader#react-native-headless"
161-->[:expo-modules-core] D:\side_project\dew_fe\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
162            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
162-->[:expo-modules-core] D:\side_project\dew_fe\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
163        <meta-data
163-->[:expo-modules-core] D:\side_project\dew_fe\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
164            android:name="com.facebook.soloader.enabled"
164-->[:expo-modules-core] D:\side_project\dew_fe\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
165            android:value="true" />
165-->[:expo-modules-core] D:\side_project\dew_fe\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
166
167        <activity
167-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
168            android:name="com.facebook.react.devsupport.DevSettingsActivity"
168-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
169            android:exported="false" />
169-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
170        <activity
170-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
171            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
171-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
172            android:excludeFromRecents="true"
172-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
173            android:exported="false"
173-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
174            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
174-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
175        <!--
176            Service handling Google Sign-In user revocation. For apps that do not integrate with
177            Google Sign-In, this service will never be started.
178        -->
179        <service
179-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
180            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
180-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
181            android:exported="true"
181-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
182            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
182-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
183            android:visibleToInstantApps="true" />
183-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\05acaadcc53fae505c061da198cb3b32\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
184        <service
184-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:28:9-40:19
185            android:name="com.google.android.gms.metadata.ModuleDependencies"
185-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:29:13-78
186            android:enabled="false"
186-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:30:13-36
187            android:exported="false" >
187-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:31:13-37
188            <intent-filter>
188-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:33:13-35:29
189                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
189-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:17-94
189-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:34:25-91
190            </intent-filter>
191
192            <meta-data
192-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:37:13-39:36
193                android:name="photopicker_activity:0:required"
193-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:38:17-63
194                android:value="" />
194-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:39:17-33
195        </service>
196
197        <activity
197-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:42:9-44:59
198            android:name="com.canhub.cropper.CropImageActivity"
198-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:43:13-64
199            android:exported="true"
199-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:33:13-36
200            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
200-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:44:13-56
201        <provider
201-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:46:9-54:20
202            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
202-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:47:13-89
203            android:authorities="com.javedakeeb.ai_therapist.ImagePickerFileProvider"
203-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:48:13-75
204            android:exported="false"
204-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:49:13-37
205            android:grantUriPermissions="true" >
205-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6d1a9aa7b559bffeebd431b87b239ac\transformed\expo.modules.imagepicker-16.1.4\AndroidManifest.xml:50:13-47
206            <meta-data
206-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
207                android:name="android.support.FILE_PROVIDER_PATHS"
207-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
208                android:resource="@xml/image_picker_provider_paths" />
208-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
209        </provider>
210        <provider
210-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:21:9-29:20
211            android:name="com.canhub.cropper.CropFileProvider"
211-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:22:13-63
212            android:authorities="com.javedakeeb.ai_therapist.cropper.fileprovider"
212-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:23:13-72
213            android:exported="false"
213-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:24:13-37
214            android:grantUriPermissions="true" >
214-->[com.vanniktech:android-image-cropper:4.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35a00de50ceac9bdbb8fc43812824536\transformed\android-image-cropper-4.6.0\AndroidManifest.xml:25:13-47
215            <meta-data
215-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
216                android:name="android.support.FILE_PROVIDER_PATHS"
216-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
217                android:resource="@xml/library_file_paths" />
217-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
218        </provider>
219
220        <activity
220-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
221            android:name="com.google.android.gms.common.api.GoogleApiActivity"
221-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
222            android:exported="false"
222-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
223            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
223-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca5f3ad87bb5a176fcf5402bbea57c24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
224
225        <meta-data
225-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\667e71e4345aed7ed3545c710439fc52\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
226            android:name="com.google.android.gms.version"
226-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\667e71e4345aed7ed3545c710439fc52\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
227            android:value="@integer/google_play_services_version" />
227-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\667e71e4345aed7ed3545c710439fc52\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
228        <meta-data
228-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1b3b9d19bcbd117df3dd62dbe0cebea\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
229            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
229-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1b3b9d19bcbd117df3dd62dbe0cebea\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
230            android:value="GlideModule" />
230-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1b3b9d19bcbd117df3dd62dbe0cebea\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
231
232        <provider
232-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
233            android:name="expo.modules.filesystem.FileSystemFileProvider"
233-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
234            android:authorities="com.javedakeeb.ai_therapist.FileSystemFileProvider"
234-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
235            android:exported="false"
235-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
236            android:grantUriPermissions="true" >
236-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
237            <meta-data
237-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
238                android:name="android.support.FILE_PROVIDER_PATHS"
238-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
239                android:resource="@xml/file_system_provider_paths" />
239-->[:react-native-webview] D:\side_project\dew_fe\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
240        </provider>
241        <provider
241-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
242            android:name="androidx.startup.InitializationProvider"
242-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
243            android:authorities="com.javedakeeb.ai_therapist.androidx-startup"
243-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
244            android:exported="false" >
244-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
245            <meta-data
245-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
246                android:name="androidx.emoji2.text.EmojiCompatInitializer"
246-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
247                android:value="androidx.startup" />
247-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
248            <meta-data
248-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
249                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
249-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
250                android:value="androidx.startup" />
250-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
251            <meta-data
251-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
252                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
252-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
253                android:value="androidx.startup" />
253-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
254        </provider>
255
256        <receiver
256-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
257            android:name="androidx.profileinstaller.ProfileInstallReceiver"
257-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
258            android:directBootAware="false"
258-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
259            android:enabled="true"
259-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
260            android:exported="true"
260-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
261            android:permission="android.permission.DUMP" >
261-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
262            <intent-filter>
262-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
263                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
263-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
263-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
264            </intent-filter>
265            <intent-filter>
265-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
266                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
266-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
266-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
267            </intent-filter>
268            <intent-filter>
268-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
269                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
269-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
269-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
270            </intent-filter>
271            <intent-filter>
271-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
272                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
272-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
272-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12c79ce362966d6e50c33af90f7764f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
273            </intent-filter>
274        </receiver>
275    </application>
276
277</manifest>
