{"name": "ai_therapist", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-google-signin/google-signin": "^15.0.0", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.6", "@react-navigation/elements": "^2.6.3", "@react-navigation/native": "^7.1.17", "@supabase/supabase-js": "^2.52.0", "expo": "~53.0.0", "expo-blur": "~14.1.5", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-edge-to-edge": "^1.6.0", "react-native-gesture-handler": "^2.24.0", "react-native-gifted-charts": "^1.4.63", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-web": "~0.20.0", "react-native-webview": "^13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@tailwindcss/postcss": "^4.1.7", "@tsconfig/react-native": "^3.0.6", "@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.27.0", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.28.0", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "postcss": "^8.5.3", "supabase": "^2.34.3", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "private": true}