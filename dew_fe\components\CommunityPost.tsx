import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { Pressable, Text, View, Image, Modal, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';

interface CommunityPostProps {
  id?: string;
  user_id?: string;
  userInitials: string;
  userName: string;
  timeAgo: string;
  soberDays: number;
  content: string;
  likes: number;
  supports: number;
  comments: number;
  userColor: string;
  image?: string;
  mood?: string;
  isDetailView?: boolean;
  isLiked?: boolean;
  isSupported?: boolean;
  currentUserId?: string;
  onLike?: (postId: string) => void;
  onSupport?: (postId: string) => void;
  onComment?: (postId: string) => void;
  onEdit?: (postId: string) => void;
  onDelete?: (postId: string) => void;
}

const CommunityPost = ({
  id,
  user_id,
  userInitials,
  userName,
  timeAgo,
  soberDays,
  content,
  likes,
  supports,
  comments,
  userColor,
  image,
  mood,
  isDetailView = false,
  isLiked = false,
  isSupported = false,
  currentUserId,
  onLike,
  onSupport,
  onComment,
  onEdit,
  onDelete
}: CommunityPostProps) => {
  const router = useRouter();
  const [showMenu, setShowMenu] = useState(false);

  // Check if current user owns this post
  const isOwnPost = currentUserId && user_id && currentUserId === user_id;

  const getSoberBadge = (days: number) => {
    if (days >= 90) return `${days} days sober 🏆`;
    if (days >= 30) return `${days} days sober ⭐`;
    return `${days} days sober 🎉`;
  };

  const getMoodData = (moodValue?: string) => {
    const moodMap: { [key: string]: { emoji: string; label: string } } = {
      'happy': { emoji: '😊', label: 'Happy' },
      'strong': { emoji: '💪', label: 'Strong' },
      'thoughtful': { emoji: '🤔', label: 'Thoughtful' },
      'peaceful': { emoji: '😌', label: 'Peaceful' },
      'struggling': { emoji: '😰', label: 'Struggling' },
      'celebrating': { emoji: '🎉', label: 'Celebrating' }
    };
    return moodValue ? moodMap[moodValue] || { emoji: '😊', label: 'Happy' } : null;
  };

  const handlePress = () => {
    if (!isDetailView && id) {
      router.push({
        pathname: '/post/[id]',
        params: { id: id }
      });
    }
  };

  return (
    <Pressable 
      className="rounded-2xl shadow-lg shadow-black/50 p-5 mb-4"
      style={{
        backgroundColor: Colors.background.elevated,
        borderWidth: 1,
        borderColor: 'rgba(255,255,255,0.03)'
      }}
      onPress={handlePress}
      disabled={isDetailView}
    >
      <View className="flex-row items-center mb-3">
        <View className={`w-12 h-12 rounded-full ${userColor} mr-3 items-center justify-center`}>
          <Text className="text-lg">{userInitials}</Text>
        </View>
        <View className="flex-1">
          <View className="flex-row items-center justify-between">
            <Text style={{ color: Colors.text.primary }} className="font-bold">
              {userName}
            </Text>
            <View className="px-3 py-1 rounded-full" style={{ backgroundColor: 'rgba(34,197,94,0.15)' }}>
              <Text style={{ color: Colors.brand.primary }} className="text-sm font-medium">
                {getSoberBadge(soberDays)}
              </Text>
            </View>
          </View>
          <View className="flex-row items-center">
            <Text style={{ color: Colors.text.secondary }} className="text-sm">
              {timeAgo}
            </Text>
            {mood && getMoodData(mood) && (
              <View className="ml-2 flex-row items-center">
                <Text style={{ color: Colors.text.secondary }} className="text-sm">•</Text>
                <Text className="ml-1 text-base">{getMoodData(mood)?.emoji}</Text>
                <Text style={{ color: Colors.text.secondary }} className="ml-1 text-sm">
                  {getMoodData(mood)?.label}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
      <Text style={{ color: Colors.text.secondary }} className="leading-relaxed mb-4">
        {content}
      </Text>
      
      {/* Image if present */}
      {image && (
        <View className="mb-4 rounded-2xl overflow-hidden shadow-lg shadow-black/50">
          <Image 
            source={{ uri: image }} 
            className="w-full h-48"
            style={{ resizeMode: 'cover' }}
          />
        </View>
      )}
      <View className="flex-row justify-between items-center">
        <View className="flex-row">
          <Pressable
            className="flex-row items-center mr-6"
            onPress={(e) => {
              e.stopPropagation();
              if (onLike && id) {
                onLike(id);
              }
            }}
          >
            <Text className="mr-1" style={{ opacity: isLiked ? 1 : 0.6 }}>
              {isLiked ? '👍' : '👍'}
            </Text>
            <Text style={{
              color: isLiked ? Colors.brand.primary : Colors.text.secondary,
              fontWeight: isLiked ? 'bold' : 'normal'
            }}>
              {likes}
            </Text>
          </Pressable>
          <Pressable
            className="flex-row items-center mr-6"
            onPress={(e) => {
              e.stopPropagation();
              if (onSupport && id) {
                onSupport(id);
              }
            }}
          >
            <Text className="mr-1" style={{ opacity: isSupported ? 1 : 0.6 }}>
              {isSupported ? '💪' : '💪'}
            </Text>
            <Text style={{
              color: isSupported ? Colors.status.success : Colors.text.secondary,
              fontWeight: isSupported ? 'bold' : 'normal'
            }}>
              {supports}
            </Text>
          </Pressable>
          <Pressable
            className="flex-row items-center"
            onPress={(e) => {
              e.stopPropagation();
              if (onComment && id) {
                onComment(id);
              }
            }}
          >
            <Text className="mr-1">💬</Text>
            <Text style={{ color: Colors.text.secondary }}>
              {comments} {comments === 1 ? 'comment' : 'comments'}
            </Text>
          </Pressable>
        </View>

        {/* 3-dots menu - only show for own posts */}
        {isOwnPost && (
          <View style={{ position: 'relative' }}>
            <Pressable
              onPress={(e) => {
                e.stopPropagation();
                setShowMenu(!showMenu);
              }}
              className="p-2"
            >
              <Ionicons name="ellipsis-vertical" size={20} color={Colors.text.secondary} />
            </Pressable>

            {/* Dropdown Menu */}
            {showMenu && (
              <View
                className="absolute right-0 top-10 rounded-lg shadow-lg z-50"
                style={{
                  backgroundColor: Colors.background.elevated,
                  borderWidth: 1,
                  borderColor: 'rgba(255,255,255,0.1)',
                  minWidth: 120,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.25,
                  shadowRadius: 8,
                  elevation: 8
                }}
              >
                <TouchableOpacity
                  onPress={(e) => {
                    e.stopPropagation();
                    setShowMenu(false);
                    if (onEdit && id) {
                      onEdit(id);
                    }
                  }}
                  className="flex-row items-center px-4 py-3 border-b"
                  style={{ borderBottomColor: 'rgba(255,255,255,0.1)' }}
                >
                  <Ionicons name="pencil" size={16} color={Colors.text.secondary} />
                  <Text style={{ color: Colors.text.primary }} className="ml-3 font-medium">
                    Edit
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={(e) => {
                    e.stopPropagation();
                    setShowMenu(false);
                    if (onDelete && id) {
                      onDelete(id);
                    }
                  }}
                  className="flex-row items-center px-4 py-3"
                >
                  <Ionicons name="trash" size={16} color={Colors.status.error} />
                  <Text style={{ color: Colors.status.error }} className="ml-3 font-medium">
                    Delete
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        )}
      </View>

      {/* Overlay to close menu when clicking outside */}
      {showMenu && (
        <Modal
          transparent
          visible={showMenu}
          onRequestClose={() => setShowMenu(false)}
        >
          <TouchableOpacity
            style={{ flex: 1 }}
            activeOpacity={1}
            onPress={() => setShowMenu(false)}
          />
        </Modal>
      )}
    </Pressable>
  );
};

export default CommunityPost; 