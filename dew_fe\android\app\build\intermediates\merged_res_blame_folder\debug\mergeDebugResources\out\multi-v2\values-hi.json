{"logs": [{"outputFile": "com.javedakeeb.ai_therapist.app-mergeDebugResources-64:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,375", "endColumns": "105,101,111,102", "endOffsets": "156,258,370,473"}, "to": {"startLines": "69,75,76,77", "startColumns": "4,4,4,4", "startOffsets": "7001,7477,7579,7691", "endColumns": "105,101,111,102", "endOffsets": "7102,7574,7686,7789"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,274,343,423,490,557,631,707,790,871,939,1018,1096,1171,1258,1344,1419,1493,1567,1654,1726,1801,1870", "endColumns": "68,78,70,68,79,66,66,73,75,82,80,67,78,77,74,86,85,74,73,73,86,71,74,68,72", "endOffsets": "119,198,269,338,418,485,552,626,702,785,866,934,1013,1091,1166,1253,1339,1414,1488,1562,1649,1721,1796,1865,1938"}, "to": {"startLines": "33,49,88,95,96,98,112,113,114,162,163,164,165,170,171,172,173,174,175,176,177,179,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2991,4573,9132,9588,9657,9796,10788,10855,10929,14897,14980,15061,15129,15528,15606,15681,15768,15854,15929,16003,16077,16265,16337,16412,16481", "endColumns": "68,78,70,68,79,66,66,73,75,82,80,67,78,77,74,86,85,74,73,73,86,71,74,68,72", "endOffsets": "3055,4647,9198,9652,9732,9858,10850,10924,11000,14975,15056,15124,15203,15601,15676,15763,15849,15924,15998,16072,16159,16332,16407,16476,16549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\35a00de50ceac9bdbb8fc43812824536\\transformed\\android-image-cropper-4.6.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,109,154,220,287,371,425", "endColumns": "53,44,65,66,83,53,64", "endOffsets": "104,149,215,282,366,420,485"}, "to": {"startLines": "70,89,90,91,92,93,161", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7107,9203,9248,9314,9381,9465,14832", "endColumns": "53,44,65,66,83,53,64", "endOffsets": "7156,9243,9309,9376,9460,9514,14892"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ca5f3ad87bb5a176fcf5402bbea57c24\\transformed\\play-services-base-18.5.0\\res\\values-hi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,575,683,830,956,1064,1172,1325,1430,1592,1718,1855,2004,2063,2126", "endColumns": "103,155,121,107,146,125,107,107,152,104,161,125,136,148,58,62,83", "endOffsets": "296,452,574,682,829,955,1063,1171,1324,1429,1591,1717,1854,2003,2062,2125,2209"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4652,4760,4920,5046,5158,5309,5439,5551,5809,5966,6075,6241,6371,6512,6665,6728,6795", "endColumns": "107,159,125,111,150,129,111,111,156,108,165,129,140,152,62,66,87", "endOffsets": "4755,4915,5041,5153,5304,5434,5546,5658,5961,6070,6236,6366,6507,6660,6723,6790,6878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\667e71e4345aed7ed3545c710439fc52\\transformed\\play-services-basement-18.4.0\\res\\values-hi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5663", "endColumns": "145", "endOffsets": "5804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,263,380,511,643,788,923,1076,1179,1308,1467", "endColumns": "117,89,116,130,131,144,134,152,102,128,158,133", "endOffsets": "168,258,375,506,638,783,918,1071,1174,1303,1462,1596"}, "to": {"startLines": "68,71,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6883,7161,7794,7911,8042,8174,8319,8454,8607,8710,8839,8998", "endColumns": "117,89,116,130,131,144,134,152,102,128,158,133", "endOffsets": "6996,7246,7906,8037,8169,8314,8449,8602,8705,8834,8993,9127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,415,513,623,709,811,932,1010,1087,1178,1271,1366,1460,1560,1653,1748,1842,1933,2024,2105,2210,2312,2410,2520,2623,2732,2890,15208", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "410,508,618,704,806,927,1005,1082,1173,1266,1361,1455,1555,1648,1743,1837,1928,2019,2100,2205,2307,2405,2515,2618,2727,2885,2986,15285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "39,40,41,42,43,44,45,178", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3498,3596,3699,3804,3905,4018,4124,16164", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3591,3694,3799,3900,4013,4119,4246,16260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,413,494,601,697,804,936,1019,1086,1151,1245,1314,1373,1458,1521,1584,1642,1707,1768,1829,1935,1993,2053,2112,2182,2298,2377,2468,2561,2659,2739,2873,2948,3024,3161,3258,3356,3413,3468,3534,3604,3681,3752,3837,3905,3981,4062,4140,4241,4327,4414,4511,4610,4684,4754,4858,4912,4999,5066,5156,5248,5310,5374,5437,5503,5608,5718,5819,5926,5987,6046,6125,6210,6290", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,80,106,95,106,131,82,66,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,90,92,97,79,133,74,75,136,96,97,56,54,65,69,76,70,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78,84,79,72", "endOffsets": "254,332,408,489,596,692,799,931,1014,1081,1146,1240,1309,1368,1453,1516,1579,1637,1702,1763,1824,1930,1988,2048,2107,2177,2293,2372,2463,2556,2654,2734,2868,2943,3019,3156,3253,3351,3408,3463,3529,3599,3676,3747,3832,3900,3976,4057,4135,4236,4322,4409,4506,4605,4679,4749,4853,4907,4994,5061,5151,5243,5305,5369,5432,5498,5603,5713,5814,5921,5982,6041,6120,6205,6285,6358"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,72,73,74,94,97,99,100,101,102,103,104,105,106,107,108,109,110,111,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3060,3138,3214,3295,3402,4251,4358,4490,7251,7318,7383,9519,9737,9863,9948,10011,10074,10132,10197,10258,10319,10425,10483,10543,10602,10672,11005,11084,11175,11268,11366,11446,11580,11655,11731,11868,11965,12063,12120,12175,12241,12311,12388,12459,12544,12612,12688,12769,12847,12948,13034,13121,13218,13317,13391,13461,13565,13619,13706,13773,13863,13955,14017,14081,14144,14210,14315,14425,14526,14633,14694,14753,15290,15375,15455", "endLines": "5,34,35,36,37,38,46,47,48,72,73,74,94,97,99,100,101,102,103,104,105,106,107,108,109,110,111,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,167,168,169", "endColumns": "12,77,75,80,106,95,106,131,82,66,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,90,92,97,79,133,74,75,136,96,97,56,54,65,69,76,70,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78,84,79,72", "endOffsets": "304,3133,3209,3290,3397,3493,4353,4485,4568,7313,7378,7472,9583,9791,9943,10006,10069,10127,10192,10253,10314,10420,10478,10538,10597,10667,10783,11079,11170,11263,11361,11441,11575,11650,11726,11863,11960,12058,12115,12170,12236,12306,12383,12454,12539,12607,12683,12764,12842,12943,13029,13116,13213,13312,13386,13456,13560,13614,13701,13768,13858,13950,14012,14076,14139,14205,14310,14420,14521,14628,14689,14748,14827,15370,15450,15523"}}]}]}