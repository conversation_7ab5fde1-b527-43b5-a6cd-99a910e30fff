import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useState, useEffect, useCallback } from 'react';
import {
  Pressable,
  ScrollView,
  Text,
  TextInput,
  View,
  ActivityIndicator,
  RefreshControl,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import CommunityPost from '../../components/CommunityPost';
import PostComment from '../../components/PostComment';
import Colors from '../../constants/Colors';
import { fetchPostDetail, submitComment, deletePost, deleteComment, PostDetailData, CommentData } from '../../utils/postApi';
import { useUserId } from '../../hooks/useAuthWithRefresh';
import SubmitCard from '../../components/SubmitCard';
import ConfirmationModal from '../../components/ConfirmationModal';
import { useSubmitCard, SUBMIT_CARD_PRESETS } from '../../hooks/useSubmitCard';

const PostDetail = () => {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const userId = useUserId();
  const {
    isVisible: submitCardVisible,
    config: submitCardConfig,
    hideSubmitCard,
    showSuccess,
    showError,
    showWarning,
  } = useSubmitCard();

  const [post, setPost] = useState<PostDetailData | null>(null);
  const [comments, setComments] = useState<CommentData[]>([]);
  const [newComment, setNewComment] = useState('');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [showDeleteCommentConfirm, setShowDeleteCommentConfirm] = useState(false);
  const [commentToDelete, setCommentToDelete] = useState<string>('');
  const [deletingComment, setDeletingComment] = useState(false);

  // Fetch post data
  const loadPostData = useCallback(async (showLoader = true) => {
    if (!id || typeof id !== 'string') return;

    if (showLoader) setLoading(true);
    try {
      const result = await fetchPostDetail(id, userId || undefined);

      if (result.error) {
        showError('Failed to Load', result.error, { emoji: '📝' });
        return;
      }

      setPost(result.post);
      setComments(result.comments);
      console.log('✅ Post data loaded successfully');

    } catch (error) {
      console.error('❌ Error loading post:', error);
      showError('Unexpected Error', 'Something went wrong while loading the post.', { emoji: '⚠️' });
    } finally {
      if (showLoader) setLoading(false);
    }
  }, [id, userId, showError]);

  // Refresh data
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadPostData(false);
    setRefreshing(false);
  }, [loadPostData]);

  // Submit comment
  const handleSubmitComment = useCallback(async () => {
    if (!newComment.trim()) {
      showWarning('Empty Comment', 'Please write something before posting.', { emoji: '✍️' });
      return;
    }

    if (!userId) {
      showWarning('Login Required', 'Please log in to post a comment.', { emoji: '🔐' });
      return;
    }

    if (!id || typeof id !== 'string') {
      showError('Invalid Post', 'Cannot post comment to this post.', { emoji: '❌' });
      return;
    }

    setSubmitting(true);
    try {
      const result = await submitComment(id, userId, newComment.trim(), replyingTo || undefined);

      if (!result.success) {
        showError('Failed to Post', result.error || 'Could not post your comment.', { emoji: '💬' });
        return;
      }

      setNewComment('');
      setReplyingTo(null);
      await loadPostData(false);

    } catch (error) {
      console.error('❌ Error submitting comment:', error);
      showError('Unexpected Error', 'Something went wrong while posting your comment.', { emoji: '⚠️' });
    } finally {
      setSubmitting(false);
    }
  }, [newComment, userId, id, replyingTo, loadPostData, showWarning, showError]);

  // Handle delete post
  const handleDeletePost = useCallback(async (postId: string) => {
    if (!userId) {
      showWarning('Login Required', 'Please log in to delete posts.', { emoji: '🔐' });
      return;
    }

    // Show confirmation modal
    setShowDeleteConfirm(true);
  }, [userId, showWarning]);

  // Confirm delete post
  const confirmDeletePost = useCallback(async () => {
    if (!id || typeof id !== 'string' || !userId) return;

    setDeleting(true);
    try {
      const result = await deletePost(id, userId);

      if (!result.success) {
        setShowDeleteConfirm(false);
        setDeleting(false);
        showError('Delete Failed', result.error || 'Could not delete the post.', { emoji: '❌' });
        return;
      }

      setShowDeleteConfirm(false);
      setDeleting(false);

      showSuccess('Post Deleted', 'Your post has been deleted successfully.', {
        emoji: '✅',
        autoClose: true,
        autoCloseDelay: 2000
      });

      // Navigate back to community after a short delay
      setTimeout(() => {
        router.back();
      }, 1500);

    } catch (error) {
      console.error('❌ Error deleting post:', error);
      setShowDeleteConfirm(false);
      setDeleting(false);
      showError('Unexpected Error', 'Something went wrong while deleting the post.', { emoji: '⚠️' });
    }
  }, [id, userId, showError, showSuccess, router]);

  // Handle delete comment
  const handleDeleteComment = useCallback(async (commentId: string) => {
    console.log('🗑️ Delete comment handler called with ID:', commentId);
    console.log('🔍 Current user ID:', userId);

    if (!userId) {
      showWarning('Login Required', 'Please log in to delete comments.', { emoji: '🔐' });
      return;
    }

    // Show confirmation modal
    setCommentToDelete(commentId);
    setShowDeleteCommentConfirm(true);
    console.log('✅ Showing delete confirmation modal');
  }, [userId, showWarning]);

  // Confirm delete comment
  const confirmDeleteComment = useCallback(async () => {
    if (!commentToDelete || !userId) return;

    setDeletingComment(true);
    try {
      const result = await deleteComment(commentToDelete, userId);

      if (!result.success) {
        setShowDeleteCommentConfirm(false);
        setDeletingComment(false);
        showError('Delete Failed', result.error || 'Could not delete the comment.', { emoji: '❌' });
        return;
      }

      setShowDeleteCommentConfirm(false);
      setDeletingComment(false);
      setCommentToDelete('');

      showSuccess('Comment Deleted', 'Your comment has been deleted successfully.', {
        emoji: '✅',
        autoClose: true,
        autoCloseDelay: 2000
      });

      // Reload post data to refresh comments
      await loadPostData(false);

    } catch (error) {
      console.error('❌ Error deleting comment:', error);
      setShowDeleteCommentConfirm(false);
      setDeletingComment(false);
      showError('Unexpected Error', 'Something went wrong while deleting the comment.', { emoji: '⚠️' });
    }
  }, [commentToDelete, userId, showError, showSuccess, loadPostData]);

  // Handle reply to comment
  const handleReply = useCallback((commentId: string) => {
    console.log('💬 Reply handler called for comment:', commentId);
    setReplyingTo(commentId);

    // Find the comment to show who we're replying to
    const findComment = (comments: CommentData[], id: string): CommentData | null => {
      for (const comment of comments) {
        if (comment.id === id) return comment;
        if (comment.replies_list) {
          const found = findComment(comment.replies_list, id);
          if (found) return found;
        }
      }
      return null;
    };

    const targetComment = findComment(comments, commentId);
    if (targetComment) {
      console.log('✅ Replying to:', targetComment.userName);
    }
  }, [comments]);

  // Load data on mount
  useEffect(() => {
    loadPostData();
  }, [loadPostData]);

  if (loading) {
    return (
      <View style={{ flex: 1, backgroundColor: Colors.background.primary }}>
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color={Colors.brand.primary} />
          <Text style={{ color: Colors.text.secondary }} className="mt-4">
            Loading post...
          </Text>
        </View>
      </View>
    );
  }

  if (!post) {
    return (
      <View style={{ flex: 1, backgroundColor: Colors.background.primary }}>
        <View className="flex-1 justify-center items-center p-8">
          <Text className="text-6xl mb-4">📝</Text>
          <Text style={{ color: Colors.text.primary }} className="text-xl font-bold mb-2 text-center">
            Post Not Found
          </Text>
          <Text style={{ color: Colors.text.secondary }} className="text-center">
            This post may have been deleted or doesn&apos;t exist.
          </Text>
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={{ flex: 1, backgroundColor: Colors.background.primary }}>
        <ScrollView
          className="flex-1"
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={Colors.brand.primary}
              colors={[Colors.brand.primary]}
            />
          }
          showsVerticalScrollIndicator={false}
        >
          <View className="p-5">
            {/* Post Content */}
            <CommunityPost
              {...post}
              isDetailView
              currentUserId={userId || undefined}
              onEdit={(postId) => {
                // TODO: Implement edit functionality
                console.log('Edit post:', postId);
              }}
              onDelete={handleDeletePost}
            />

     

            {/* Comments Section */}
            <View className="mt-8">
              <View className="flex-row items-center justify-between mb-6">
                <Text style={{ color: Colors.text.primary }} className="text-xl font-bold">
                  Comments
                </Text>
                {comments.length > 0 && (
                  <View
                    className="px-3 py-1 rounded-full"
                    style={{ backgroundColor: Colors.brand.primary + '20' }}
                  >
                    <Text style={{ color: Colors.brand.primary }} className="text-sm font-bold">
                      {comments.length}
                    </Text>
                  </View>
                )}
              </View>

              {comments.length === 0 ? (
                <View className="py-12 items-center">
                  <View
                    className="w-16 h-16 rounded-full items-center justify-center mb-4"
                    style={{ backgroundColor: Colors.background.elevated }}
                  >
                    <Text className="text-3xl">💬</Text>
                  </View>
                  <Text style={{ color: Colors.text.primary }} className="text-lg font-bold mb-2">
                    No comments yet
                  </Text>
                  <Text style={{ color: Colors.text.secondary }} className="text-center px-8">
                    Be the first to share your thoughts on this post!
                  </Text>
                </View>
              ) : (
                <View>
                  {comments.map((comment) => (
                    <View key={comment.id} className="mb-4">
                      <PostComment
                        {...comment}
                        currentUserId={userId || undefined}
                        onEdit={(commentId) => {
                          // TODO: Implement edit functionality
                          console.log('Edit comment:', commentId);
                        }}
                        onDelete={handleDeleteComment}
                        onReply={handleReply}
                      />
                      {comment.replies_list?.map((reply) => (
                        <PostComment
                          key={reply.id}
                          {...reply}
                          isReply
                          currentUserId={userId || undefined}
                          onEdit={(commentId) => {
                            // TODO: Implement edit functionality
                            console.log('Edit reply:', commentId);
                          }}
                          onDelete={handleDeleteComment}
                        />
                      ))}
                    </View>
                  ))}
                </View>
              )}
            </View>
          </View>
        </ScrollView>

        {/* Comment Input */}
        <View
          className="p-4 border-t"
          style={{
            backgroundColor: Colors.background.elevated,
            borderTopColor: 'rgba(255,255,255,0.1)',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: -2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 5
          }}
        >
          {replyingTo && (
            <View className="flex-row items-center mb-3 p-3 rounded-lg" style={{ backgroundColor: Colors.background.secondary }}>
              <View className="flex-1">
                <Text style={{ color: Colors.text.primary }} className="text-sm font-medium">
                  💬 Replying to comment
                </Text>
                <Text style={{ color: Colors.text.secondary }} className="text-xs mt-1">
                  Your reply will appear nested under the original comment
                </Text>
              </View>
              <Pressable onPress={() => setReplyingTo(null)} className="p-1">
                <Ionicons name="close" size={18} color={Colors.text.secondary} />
              </Pressable>
            </View>
          )}

          <View className="flex-row items-end">
            <TextInput
              value={newComment}
              onChangeText={setNewComment}
              placeholder={
                !userId
                  ? "Please log in to comment"
                  : replyingTo
                    ? "Write your reply..."
                    : "Write a thoughtful comment..."
              }
              placeholderTextColor={Colors.text.secondary}
              editable={!!userId}
              style={{
                color: Colors.text.primary,
                backgroundColor: Colors.background.primary,
                borderColor: 'rgba(255,255,255,0.1)',
                borderWidth: 1.5,
                borderRadius: 20,
                paddingHorizontal: 16,
                paddingVertical: 12,
                flex: 1,
                marginRight: 12,
                maxHeight: 100,
                minHeight: 44,
                fontSize: 16
              }}
              multiline
              textAlignVertical="top"
              maxLength={1000}
            />

            <Pressable
              onPress={handleSubmitComment}
              disabled={!newComment.trim() || submitting || !userId}
              className="p-3 rounded-full"
              style={{
                backgroundColor: (newComment.trim() && userId) ? Colors.brand.primary : Colors.background.secondary,
                opacity: submitting ? 0.6 : 1
              }}
            >
              {submitting ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Ionicons
                  name="send"
                  size={20}
                  color={(newComment.trim() && userId) ? 'white' : Colors.text.secondary}
                />
              )}
            </Pressable>
          </View>
        </View>
      </View>

      {/* Submit Card */}
      {submitCardConfig && (
        <SubmitCard
          visible={submitCardVisible}
          type={submitCardConfig.type}
          title={submitCardConfig.title}
          message={submitCardConfig.message}
          onClose={hideSubmitCard}
          onAction={submitCardConfig.onAction}
          actionText={submitCardConfig.actionText}
          autoClose={submitCardConfig.autoClose}
          autoCloseDelay={submitCardConfig.autoCloseDelay}
          emoji={submitCardConfig.emoji}
        />
      )}

      {/* Delete Post Confirmation Modal */}
      <ConfirmationModal
        visible={showDeleteConfirm}
        title="Delete Post?"
        message="This action cannot be undone. Are you sure you want to delete this post?"
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDeletePost}
        onCancel={() => setShowDeleteConfirm(false)}
        loading={deleting}
        emoji="🗑️"
        destructive={true}
      />

      {/* Delete Comment Confirmation Modal */}
      <ConfirmationModal
        visible={showDeleteCommentConfirm}
        title="Delete Comment?"
        message="This action cannot be undone. Are you sure you want to delete this comment?"
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDeleteComment}
        onCancel={() => {
          setShowDeleteCommentConfirm(false);
          setCommentToDelete('');
        }}
        loading={deletingComment}
        emoji="🗑️"
        destructive={true}
      />
    </KeyboardAvoidingView>
  );
};

export default PostDetail; 