{"logs": [{"outputFile": "com.javedakeeb.ai_therapist.app-mergeDebugResources-64:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\667e71e4345aed7ed3545c710439fc52\\transformed\\play-services-basement-18.4.0\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5660", "endColumns": "163", "endOffsets": "5819"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,268,382", "endColumns": "100,111,113,107", "endOffsets": "151,263,377,485"}, "to": {"startLines": "68,73,74,75", "startColumns": "4,4,4,4", "startOffsets": "7063,7476,7588,7702", "endColumns": "100,111,113,107", "endOffsets": "7159,7583,7697,7805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "38,39,40,41,42,43,44,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3522,3625,3728,3830,3936,4034,4134,14774", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3620,3723,3825,3931,4029,4129,4237,14870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,616,705,807,917,1004,1064,1130,1226,1292,1353,1458,1522,1594,1652,1726,1788,1842,1955,2015,2076,2135,2213,2337,2418,2500,2600,2685,2770,2906,2987,3070,3201,3284,3370,3432,3486,3552,3629,3708,3779,3862,3931,4007,4088,4156,4260,4351,4429,4522,4619,4693,4772,4870,4930,5018,5084,5172,5260,5322,5390,5453,5519,5624,5730,5825,5930,5996,6054,6138,6227,6303", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "257,346,434,516,611,700,802,912,999,1059,1125,1221,1287,1348,1453,1517,1589,1647,1721,1783,1837,1950,2010,2071,2130,2208,2332,2413,2495,2595,2680,2765,2901,2982,3065,3196,3279,3365,3427,3481,3547,3624,3703,3774,3857,3926,4002,4083,4151,4255,4346,4424,4517,4614,4688,4767,4865,4925,5013,5079,5167,5255,5317,5385,5448,5514,5619,5725,5820,5925,5991,6049,6133,6222,6298,6371"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,70,71,72,86,89,91,92,93,94,95,96,97,98,99,100,101,102,103,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3079,3168,3256,3338,3433,4242,4344,4454,7254,7314,7380,9123,9336,9465,9570,9634,9706,9764,9838,9900,9954,10067,10127,10188,10247,10325,10516,10597,10679,10779,10864,10949,11085,11166,11249,11380,11463,11549,11611,11665,11731,11808,11887,11958,12041,12110,12186,12267,12335,12439,12530,12608,12701,12798,12872,12951,13049,13109,13197,13263,13351,13439,13501,13569,13632,13698,13803,13909,14004,14109,14175,14233,14397,14486,14562", "endLines": "5,33,34,35,36,37,45,46,47,70,71,72,86,89,91,92,93,94,95,96,97,98,99,100,101,102,103,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,152,153,154", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "307,3163,3251,3333,3428,3517,4339,4449,4536,7309,7375,7471,9184,9392,9565,9629,9701,9759,9833,9895,9949,10062,10122,10183,10242,10320,10444,10592,10674,10774,10859,10944,11080,11161,11244,11375,11458,11544,11606,11660,11726,11803,11882,11953,12036,12105,12181,12262,12330,12434,12525,12603,12696,12793,12867,12946,13044,13104,13192,13258,13346,13434,13496,13564,13627,13693,13798,13904,13999,14104,14170,14228,14312,14481,14557,14630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,2872", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "312,421,532,640,731,838,958,1042,1121,1212,1305,1400,1494,1594,1687,1782,1876,1967,2058,2144,2257,2358,2461,2574,2684,2801,2968,14317", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "416,527,635,726,833,953,1037,1116,1207,1300,1395,1489,1589,1682,1777,1871,1962,2053,2139,2252,2353,2456,2569,2679,2796,2963,3074,14392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ca5f3ad87bb5a176fcf5402bbea57c24\\transformed\\play-services-base-18.5.0\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,454,584,697,864,996,1102,1203,1379,1489,1649,1778,1922,2070,2132,2200", "endColumns": "106,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "299,453,583,696,863,995,1101,1202,1378,1488,1648,1777,1921,2069,2131,2199,2287"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4618,4729,4887,5021,5138,5309,5445,5555,5824,6004,6118,6282,6415,6563,6715,6781,6853", "endColumns": "110,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "4724,4882,5016,5133,5304,5440,5550,5655,5999,6113,6277,6410,6558,6710,6776,6848,6940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,263,385,513,645,790,922,1070,1166,1306,1445", "endColumns": "117,89,121,127,131,144,131,147,95,139,138,130", "endOffsets": "168,258,380,508,640,785,917,1065,1161,1301,1440,1571"}, "to": {"startLines": "67,69,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6945,7164,7810,7932,8060,8192,8337,8469,8617,8713,8853,8992", "endColumns": "117,89,121,127,131,144,131,147,95,139,138,130", "endOffsets": "7058,7249,7927,8055,8187,8332,8464,8612,8708,8848,8987,9118"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,279,347,414,484", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "127,195,274,342,409,479,548"}, "to": {"startLines": "48,87,88,90,104,155,156", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4541,9189,9257,9397,10449,14635,14705", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "4613,9252,9331,9460,10511,14700,14769"}}]}]}