import React from 'react';
import { Pressable, Text, View } from 'react-native';
import Colors from '../constants/Colors';

interface PostCommentProps {
  id?: string;
  userInitials: string;
  userName: string;
  timeAgo: string;
  content: string;
  likes: number;
  replies: number;
  userColor: string;
  isReply?: boolean;
  isLiked?: boolean;
  onLike?: (commentId: string) => void;
  onReply?: (commentId: string) => void;
}

const PostComment = ({
  id,
  userInitials,
  userName,
  timeAgo,
  content,
  likes,
  replies,
  userColor,
  isReply = false,
  isLiked = false,
  onLike,
  onReply
}: PostCommentProps) => {
  return (
    <View className={`${isReply ? 'ml-12' : ''} mb-4`}>
      <View className="flex-row items-start">
        {/* User Avatar */}
        <View
          className="w-10 h-10 rounded-full mr-3 items-center justify-center shadow-sm"
          style={{
            backgroundColor: Colors.brand.primary + '20',
            borderWidth: 2,
            borderColor: Colors.brand.primary + '40'
          }}
        >
          <Text style={{ color: Colors.brand.primary }} className="text-sm font-bold">
            {userInitials}
          </Text>
        </View>

        <View className="flex-1">
          {/* Comment Bubble */}
          <View
            className="rounded-2xl p-4 shadow-sm"
            style={{
              backgroundColor: Colors.background.elevated,
              borderWidth: 1,
              borderColor: 'rgba(255,255,255,0.05)'
            }}
          >
            <View className="flex-row items-center justify-between mb-2">
              <Text style={{ color: Colors.text.primary }} className="font-semibold text-base">
                {userName}
              </Text>
              <Text style={{ color: Colors.text.secondary }} className="text-xs">
                {timeAgo}
              </Text>
            </View>
            <Text style={{ color: Colors.text.primary }} className="leading-6">
              {content}
            </Text>
          </View>

          {/* Action Buttons */}
          <View className="flex-row items-center mt-3 ml-2">
            <Pressable
              className="flex-row items-center mr-6"
              onPress={() => {
                if (onLike && id) {
                  onLike(id);
                }
              }}
            >
              <Text className="mr-1" style={{ opacity: isLiked ? 1 : 0.6 }}>
                👍
              </Text>
              <Text
                style={{
                  color: isLiked ? Colors.brand.primary : Colors.text.secondary,
                  fontWeight: isLiked ? 'bold' : 'normal'
                }}
                className="text-sm"
              >
                {likes}
              </Text>
            </Pressable>

            {!isReply && (
              <Pressable
                className="mr-6"
                onPress={() => {
                  if (onReply && id) {
                    onReply(id);
                  }
                }}
              >
                <Text style={{ color: Colors.text.secondary }} className="text-sm font-medium">
                  Reply
                </Text>
              </Pressable>
            )}

            {!isReply && replies > 0 && (
              <Pressable>
                <Text style={{ color: Colors.text.secondary }} className="text-sm">
                  {replies} {replies === 1 ? 'reply' : 'replies'}
                </Text>
              </Pressable>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

export default PostComment; 