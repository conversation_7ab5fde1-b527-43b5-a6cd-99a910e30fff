import React, { useState } from 'react';
import { Pressable, Text, View, Modal, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';

interface PostCommentProps {
  id?: string;
  user_id?: string;
  userInitials: string;
  userName: string;
  timeAgo: string;
  content: string;
  likes: number;
  replies: number;
  userColor: string;
  isReply?: boolean;
  isLiked?: boolean;
  currentUserId?: string;
  onLike?: (commentId: string) => void;
  onReply?: (commentId: string) => void;
  onEdit?: (commentId: string) => void;
  onDelete?: (commentId: string) => void;
}

const PostComment = ({
  id,
  user_id,
  userInitials,
  userName,
  timeAgo,
  content,
  likes,
  replies,
  userColor,
  isReply = false,
  isLiked = false,
  currentUserId,
  onLike,
  onReply,
  onEdit,
  onDelete
}: PostCommentProps) => {
  const [showMenu, setShowMenu] = useState(false);

  // Check if current user owns this comment
  const isOwnComment = currentUserId && user_id && currentUserId === user_id;

  // Debug logging
  React.useEffect(() => {
    console.log('🔍 Comment ownership check:', {
      commentId: id,
      currentUserId,
      commentUserId: user_id,
      isOwnComment
    });
  }, [id, currentUserId, user_id, isOwnComment]);
  return (
    <View className={`${isReply ? 'ml-12' : ''} mb-4`}>
      <View className="flex-row items-start">
        {/* User Avatar */}
        <View
          className="w-10 h-10 rounded-full mr-3 items-center justify-center shadow-sm"
          style={{
            backgroundColor: Colors.brand.primary + '20',
            borderWidth: 2,
            borderColor: Colors.brand.primary + '40'
          }}
        >
          <Text style={{ color: Colors.brand.primary }} className="text-sm font-bold">
            {userInitials}
          </Text>
        </View>

        <View className="flex-1">
          {/* Comment Bubble */}
          <View
            className="rounded-2xl p-4 shadow-sm"
            style={{
              backgroundColor: Colors.background.elevated,
              borderWidth: 1,
              borderColor: 'rgba(255,255,255,0.05)'
            }}
          >
            <View className="flex-row items-center justify-between mb-2">
              <View className="flex-row items-center flex-1">
                <Text style={{ color: Colors.text.primary }} className="font-semibold text-base">
                  {userName}
                </Text>
                <Text style={{ color: Colors.text.secondary }} className="text-xs ml-2">
                  {timeAgo}
                </Text>
              </View>

              {/* 3-dots menu - only show for own comments */}
              {isOwnComment && (
                <View style={{ position: 'relative' }}>
                  <Pressable
                    onPress={() => setShowMenu(!showMenu)}
                    className="p-1"
                  >
                    <Ionicons name="ellipsis-vertical" size={16} color={Colors.text.secondary} />
                  </Pressable>

                  {/* Dropdown Menu */}
                  {showMenu && (
                    <View
                      className="absolute right-0 top-6 rounded-lg shadow-lg z-50"
                      style={{
                        backgroundColor: Colors.background.elevated,
                        borderWidth: 1,
                        borderColor: 'rgba(255,255,255,0.1)',
                        minWidth: 100,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.25,
                        shadowRadius: 8,
                        elevation: 8
                      }}
                    >
                      <TouchableOpacity
                        onPress={() => {
                          setShowMenu(false);
                          if (onEdit && id) {
                            onEdit(id);
                          }
                        }}
                        className="flex-row items-center px-3 py-2 border-b"
                        style={{ borderBottomColor: 'rgba(255,255,255,0.1)' }}
                      >
                        <Ionicons name="pencil" size={14} color={Colors.text.secondary} />
                        <Text style={{ color: Colors.text.primary }} className="ml-2 text-sm font-medium">
                          Edit
                        </Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        onPress={() => {
                          console.log('🗑️ Delete button clicked for comment:', id);
                          console.log('🔍 onDelete function exists:', !!onDelete);
                          setShowMenu(false);
                          if (onDelete && id) {
                            console.log('✅ Calling onDelete with ID:', id);
                            onDelete(id);
                          } else {
                            console.log('❌ onDelete not available or no ID');
                          }
                        }}
                        className="flex-row items-center px-3 py-2"
                      >
                        <Ionicons name="trash" size={14} color={Colors.status.error} />
                        <Text style={{ color: Colors.status.error }} className="ml-2 text-sm font-medium">
                          Delete
                        </Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              )}
            </View>
            <Text style={{ color: Colors.text.primary }} className="leading-6">
              {content}
            </Text>


          </View>

          {/* Action Buttons */}
          <View className="flex-row items-center mt-3 ml-2">
            <Pressable
              className="flex-row items-center mr-6"
              onPress={() => {
                if (onLike && id) {
                  onLike(id);
                }
              }}
            >
              <Text className="mr-1" style={{ opacity: isLiked ? 1 : 0.6 }}>
                👍
              </Text>
              <Text
                style={{
                  color: isLiked ? Colors.brand.primary : Colors.text.secondary,
                  fontWeight: isLiked ? 'bold' : 'normal'
                }}
                className="text-sm"
              >
                {likes}
              </Text>
            </Pressable>

            {!isReply && (
              <Pressable
                className="mr-6"
                onPress={() => {
                  console.log('💬 Reply button clicked for comment:', id);
                  if (onReply && id) {
                    onReply(id);
                  }
                }}
              >
                <Text style={{ color: Colors.text.secondary }} className="text-sm font-medium">
                  Reply
                </Text>
              </Pressable>
            )}

            {!isReply && replies > 0 && (
              <Pressable>
                <Text style={{ color: Colors.text.secondary }} className="text-sm">
                  {replies} {replies === 1 ? 'reply' : 'replies'}
                </Text>
              </Pressable>
            )}
          </View>
        </View>
      </View>

      {/* Overlay to close menu when clicking outside */}
      {showMenu && (
        <Modal
          transparent
          visible={showMenu}
          onRequestClose={() => setShowMenu(false)}
        >
          <TouchableOpacity
            style={{ flex: 1 }}
            activeOpacity={1}
            onPress={() => setShowMenu(false)}
          />
        </Modal>
      )}
    </View>
  );
};

export default PostComment; 