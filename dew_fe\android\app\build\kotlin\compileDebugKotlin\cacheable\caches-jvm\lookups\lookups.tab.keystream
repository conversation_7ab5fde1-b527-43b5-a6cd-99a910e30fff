  Application android.app  Build android.app.Activity  BuildConfig android.app.Activity  DefaultReactActivityDelegate android.app.Activity  ReactActivityDelegateWrapper android.app.Activity  SplashScreenManager android.app.Activity  
fabricEnabled android.app.Activity  moveTaskToBack android.app.Activity  onCreate android.app.Activity  registerOnActivity android.app.Activity  ApplicationLifecycleDispatcher android.app.Application  Boolean android.app.Application  BuildConfig android.app.Application  DefaultReactNativeHost android.app.Application  List android.app.Application  OpenSourceMergedSoMapping android.app.Application  PackageList android.app.Application  ReactNativeHostWrapper android.app.Application  ReactPackage android.app.Application  SoLoader android.app.Application  String android.app.Application  createReactHost android.app.Application  load android.app.Application  onApplicationCreate android.app.Application  onConfigurationChanged android.app.Application  onCreate android.app.Application  ApplicationLifecycleDispatcher android.content.Context  Boolean android.content.Context  Build android.content.Context  BuildConfig android.content.Context  DefaultReactActivityDelegate android.content.Context  DefaultReactNativeHost android.content.Context  List android.content.Context  OpenSourceMergedSoMapping android.content.Context  PackageList android.content.Context  ReactActivityDelegateWrapper android.content.Context  ReactNativeHostWrapper android.content.Context  ReactPackage android.content.Context  SoLoader android.content.Context  SplashScreenManager android.content.Context  String android.content.Context  createReactHost android.content.Context  
fabricEnabled android.content.Context  load android.content.Context  onApplicationCreate android.content.Context  onConfigurationChanged android.content.Context  registerOnActivity android.content.Context  ApplicationLifecycleDispatcher android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  BuildConfig android.content.ContextWrapper  DefaultReactActivityDelegate android.content.ContextWrapper  DefaultReactNativeHost android.content.ContextWrapper  List android.content.ContextWrapper  OpenSourceMergedSoMapping android.content.ContextWrapper  PackageList android.content.ContextWrapper  ReactActivityDelegateWrapper android.content.ContextWrapper  ReactNativeHostWrapper android.content.ContextWrapper  ReactPackage android.content.ContextWrapper  SoLoader android.content.ContextWrapper  SplashScreenManager android.content.ContextWrapper  String android.content.ContextWrapper  applicationContext android.content.ContextWrapper  createReactHost android.content.ContextWrapper  
fabricEnabled android.content.ContextWrapper  load android.content.ContextWrapper  onApplicationCreate android.content.ContextWrapper  onConfigurationChanged android.content.ContextWrapper  registerOnActivity android.content.ContextWrapper  
Configuration android.content.res  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  R android.os.Build.VERSION_CODES  Build  android.view.ContextThemeWrapper  BuildConfig  android.view.ContextThemeWrapper  DefaultReactActivityDelegate  android.view.ContextThemeWrapper  ReactActivityDelegateWrapper  android.view.ContextThemeWrapper  SplashScreenManager  android.view.ContextThemeWrapper  
fabricEnabled  android.view.ContextThemeWrapper  registerOnActivity  android.view.ContextThemeWrapper  Build #androidx.activity.ComponentActivity  BuildConfig #androidx.activity.ComponentActivity  DefaultReactActivityDelegate #androidx.activity.ComponentActivity  ReactActivityDelegateWrapper #androidx.activity.ComponentActivity  SplashScreenManager #androidx.activity.ComponentActivity  
fabricEnabled #androidx.activity.ComponentActivity  registerOnActivity #androidx.activity.ComponentActivity  Build (androidx.appcompat.app.AppCompatActivity  BuildConfig (androidx.appcompat.app.AppCompatActivity  DefaultReactActivityDelegate (androidx.appcompat.app.AppCompatActivity  ReactActivityDelegateWrapper (androidx.appcompat.app.AppCompatActivity  SplashScreenManager (androidx.appcompat.app.AppCompatActivity  
fabricEnabled (androidx.appcompat.app.AppCompatActivity  registerOnActivity (androidx.appcompat.app.AppCompatActivity  Build #androidx.core.app.ComponentActivity  BuildConfig #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  DefaultReactActivityDelegate #androidx.core.app.ComponentActivity  ReactActivityDelegate #androidx.core.app.ComponentActivity  ReactActivityDelegateWrapper #androidx.core.app.ComponentActivity  SplashScreenManager #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  
fabricEnabled #androidx.core.app.ComponentActivity  registerOnActivity #androidx.core.app.ComponentActivity  Build &androidx.fragment.app.FragmentActivity  BuildConfig &androidx.fragment.app.FragmentActivity  DefaultReactActivityDelegate &androidx.fragment.app.FragmentActivity  ReactActivityDelegateWrapper &androidx.fragment.app.FragmentActivity  SplashScreenManager &androidx.fragment.app.FragmentActivity  
fabricEnabled &androidx.fragment.app.FragmentActivity  registerOnActivity &androidx.fragment.app.FragmentActivity  PackageList com.facebook.react  
ReactActivity com.facebook.react  ReactActivityDelegate com.facebook.react  ReactApplication com.facebook.react  	ReactHost com.facebook.react  ReactNativeHost com.facebook.react  ReactPackage com.facebook.react  packages com.facebook.react.PackageList  Build  com.facebook.react.ReactActivity  BuildConfig  com.facebook.react.ReactActivity  DefaultReactActivityDelegate  com.facebook.react.ReactActivity  ReactActivityDelegateWrapper  com.facebook.react.ReactActivity  SplashScreenManager  com.facebook.react.ReactActivity  
fabricEnabled  com.facebook.react.ReactActivity  invokeDefaultOnBackPressed  com.facebook.react.ReactActivity  onCreate  com.facebook.react.ReactActivity  registerOnActivity  com.facebook.react.ReactActivity  
fabricEnabled (com.facebook.react.ReactActivityDelegate  mainComponentName (com.facebook.react.ReactActivityDelegate  BuildConfig "com.facebook.react.ReactNativeHost  PackageList "com.facebook.react.ReactNativeHost  ReactFontManager  com.facebook.react.common.assets  DefaultReactActivityDelegate com.facebook.react.defaults  DefaultReactNativeHost com.facebook.react.defaults  
fabricEnabled <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  load <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  OpenSourceMergedSoMapping com.facebook.react.soloader  SoLoader com.facebook.soloader  init com.facebook.soloader.SoLoader  Application com.javedakeeb.ai_therapist  ApplicationLifecycleDispatcher com.javedakeeb.ai_therapist  Boolean com.javedakeeb.ai_therapist  Build com.javedakeeb.ai_therapist  BuildConfig com.javedakeeb.ai_therapist  Bundle com.javedakeeb.ai_therapist  
Configuration com.javedakeeb.ai_therapist  DefaultReactActivityDelegate com.javedakeeb.ai_therapist  DefaultReactNativeHost com.javedakeeb.ai_therapist  List com.javedakeeb.ai_therapist  MainActivity com.javedakeeb.ai_therapist  MainApplication com.javedakeeb.ai_therapist  OpenSourceMergedSoMapping com.javedakeeb.ai_therapist  PackageList com.javedakeeb.ai_therapist  
ReactActivity com.javedakeeb.ai_therapist  ReactActivityDelegate com.javedakeeb.ai_therapist  ReactActivityDelegateWrapper com.javedakeeb.ai_therapist  ReactApplication com.javedakeeb.ai_therapist  	ReactHost com.javedakeeb.ai_therapist  ReactNativeHost com.javedakeeb.ai_therapist  ReactNativeHostWrapper com.javedakeeb.ai_therapist  ReactPackage com.javedakeeb.ai_therapist  SoLoader com.javedakeeb.ai_therapist  SplashScreenManager com.javedakeeb.ai_therapist  String com.javedakeeb.ai_therapist  createReactHost com.javedakeeb.ai_therapist  
fabricEnabled com.javedakeeb.ai_therapist  load com.javedakeeb.ai_therapist  onApplicationCreate com.javedakeeb.ai_therapist  onConfigurationChanged com.javedakeeb.ai_therapist  registerOnActivity com.javedakeeb.ai_therapist  DEBUG 'com.javedakeeb.ai_therapist.BuildConfig  IS_HERMES_ENABLED 'com.javedakeeb.ai_therapist.BuildConfig  IS_NEW_ARCHITECTURE_ENABLED 'com.javedakeeb.ai_therapist.BuildConfig  Build (com.javedakeeb.ai_therapist.MainActivity  BuildConfig (com.javedakeeb.ai_therapist.MainActivity  ReactActivityDelegateWrapper (com.javedakeeb.ai_therapist.MainActivity  SplashScreenManager (com.javedakeeb.ai_therapist.MainActivity  
fabricEnabled (com.javedakeeb.ai_therapist.MainActivity  mainComponentName (com.javedakeeb.ai_therapist.MainActivity  moveTaskToBack (com.javedakeeb.ai_therapist.MainActivity  registerOnActivity (com.javedakeeb.ai_therapist.MainActivity  ApplicationLifecycleDispatcher +com.javedakeeb.ai_therapist.MainApplication  BuildConfig +com.javedakeeb.ai_therapist.MainApplication  OpenSourceMergedSoMapping +com.javedakeeb.ai_therapist.MainApplication  PackageList +com.javedakeeb.ai_therapist.MainApplication  ReactNativeHostWrapper +com.javedakeeb.ai_therapist.MainApplication  SoLoader +com.javedakeeb.ai_therapist.MainApplication  applicationContext +com.javedakeeb.ai_therapist.MainApplication  createReactHost +com.javedakeeb.ai_therapist.MainApplication  load +com.javedakeeb.ai_therapist.MainApplication  onApplicationCreate +com.javedakeeb.ai_therapist.MainApplication  onConfigurationChanged +com.javedakeeb.ai_therapist.MainApplication  reactNativeHost +com.javedakeeb.ai_therapist.MainApplication  ApplicationLifecycleDispatcher expo.modules  ReactActivityDelegateWrapper expo.modules  ReactNativeHostWrapper expo.modules  onApplicationCreate +expo.modules.ApplicationLifecycleDispatcher  onConfigurationChanged +expo.modules.ApplicationLifecycleDispatcher  	Companion #expo.modules.ReactNativeHostWrapper  createReactHost #expo.modules.ReactNativeHostWrapper  createReactHost -expo.modules.ReactNativeHostWrapper.Companion  SplashScreenManager expo.modules.splashscreen  registerOnActivity -expo.modules.splashscreen.SplashScreenManager  Nothing kotlin  not kotlin.Boolean  	compareTo 
kotlin.Int  List kotlin.collections  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  ReactActivityDelegate #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Build -androidx.activity.ComponentActivity.Companion  BuildConfig -androidx.activity.ComponentActivity.Companion  ReactActivityDelegateWrapper -androidx.activity.ComponentActivity.Companion  SplashScreenManager -androidx.activity.ComponentActivity.Companion  
fabricEnabled -androidx.activity.ComponentActivity.Companion  registerOnActivity -androidx.activity.ComponentActivity.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        